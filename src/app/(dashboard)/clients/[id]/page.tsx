"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { Separator } from "@/components/ui/separator"
import { StatusBadge } from "@/components/ui/status-badge"
import { PageHeader } from "@/components/layout/page-header"
import { ClientForm } from "@/components/clients/client-form"
import { getClient, deleteClient } from "@/lib/api/clients-client"
import { getInvoicesByClient } from "@/lib/api/invoices-client"
import { getProjectsByClient } from "@/lib/api/projects-client"
import { Client, InvoiceWithRelations, ProjectWithRelations } from "@/lib/types"
import { formatInvoiceCurrency, type Currency } from "@/lib/utils/currency"
import { ArrowLeft, Edit, Trash2, Mail, Phone, Building, MapPin, Calendar, FileText } from "lucide-react"

interface ClientAddress {
  street?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
}
import { formatDistanceToNow } from "date-fns"

export default function ClientDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [client, setClient] = useState<Client | null>(null)
  const [invoices, setInvoices] = useState<InvoiceWithRelations[]>([])
  const [projects, setProjects] = useState<ProjectWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingInvoices, setLoadingInvoices] = useState(true)
  const [loadingProjects, setLoadingProjects] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)

  const fetchClient = useCallback(async () => {
    if (!params.id) return

    setLoading(true)
    try {
      const data = await getClient(params.id as string)
      setClient(data)
    } catch (error) {
      console.error("Failed to fetch client:", error)
      router.push("/clients")
    } finally {
      setLoading(false)
    }
  }, [params.id, router])

  const fetchInvoices = useCallback(async () => {
    if (!params.id) return

    setLoadingInvoices(true)
    try {
      const data = await getInvoicesByClient(params.id as string)
      setInvoices(data)
    } catch (error) {
      console.error("Failed to fetch client invoices:", error)
    } finally {
      setLoadingInvoices(false)
    }
  }, [params.id])

  const fetchProjects = useCallback(async () => {
    if (!params.id) return

    setLoadingProjects(true)
    try {
      const data = await getProjectsByClient(params.id as string)
      setProjects(data)
    } catch (error) {
      console.error("Failed to fetch client projects:", error)
    } finally {
      setLoadingProjects(false)
    }
  }, [params.id])

  useEffect(() => {
    fetchClient()
    fetchInvoices()
    fetchProjects()
  }, [fetchClient, fetchInvoices, fetchProjects])

  const handleDeleteClient = async () => {
    if (!client) return
    
    if (confirm("Are you sure you want to delete this client? This action cannot be undone.")) {
      try {
        await deleteClient(client.id)
        router.push("/clients")
      } catch (error) {
        console.error("Failed to delete client:", error)
      }
    }
  }

  const handleEditSuccess = () => {
    fetchClient()
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-6 md:p-8 pt-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="h-48 bg-muted rounded"></div>
            <div className="h-48 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="flex-1 space-y-6 p-6 md:p-8 pt-6">
        <div className="text-center py-12">
          <p className="text-muted-foreground">Client not found.</p>
          <Button onClick={() => router.push("/clients")} className="mt-6">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Clients
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6 md:p-8 pt-6">
      <PageHeader
        title={client.name}
        description={`Client details and project history`}
      >
        {/* Mobile-first responsive button layout */}
        <Button
          variant="outline"
          onClick={() => router.push("/clients")}
          className="w-full sm:w-auto"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="flex gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={() => setShowEditForm(true)}
            className="flex-1 sm:flex-none"
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteClient}
            className="flex-1 sm:flex-none"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </PageHeader>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Client Information */}
        <Card className="border-border/50 shadow-sm">
          <CardHeader className="space-y-2">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold tracking-tight">
              Client Information
              <StatusBadge status={client.status} />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              {client.email && (
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Mail className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-sm">{client.email}</span>
                </div>
              )}

              {client.phone && (
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-lg bg-emerald-50 dark:bg-emerald-950 flex items-center justify-center">
                    <Phone className="h-4 w-4 text-emerald-600" />
                  </div>
                  <span className="text-sm">{client.phone}</span>
                </div>
              )}

              {client.company && (
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-lg bg-amber-50 dark:bg-amber-950 flex items-center justify-center">
                    <Building className="h-4 w-4 text-amber-600" />
                  </div>
                  <span className="text-sm">{client.company}</span>
                </div>
              )}

              {client.lead_source && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Lead Source</p>
                  <p className="text-sm text-muted-foreground">{client.lead_source}</p>
                </div>
              )}

              {client.address && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Address</span>
                  </div>
                  <div className="text-sm text-muted-foreground ml-6">
                    {client.address && typeof client.address === 'object' && !Array.isArray(client.address) && (() => {
                      const address = client.address as ClientAddress
                      return (
                        <>
                          {address.street && <p>{address.street}</p>}
                          {(address.city || address.state || address.zipCode) && (
                            <p>
                              {[address.city, address.state, address.zipCode]
                                .filter(Boolean)
                                .join(", ")}
                            </p>
                          )}
                          {address.country && <p>{address.country}</p>}
                        </>
                      )
                    })()}
                  </div>
                </div>
              )}

              <Separator />

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>
                  Added {formatDistanceToNow(new Date(client.created_at), { addSuffix: true })}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Projects */}
        <Card>
          <CardHeader>
            <CardTitle>Projects</CardTitle>
            <CardDescription>
              Projects associated with this client
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loadingProjects ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Loading projects...</p>
              </div>
            ) : projects.length > 0 ? (
              <div className="space-y-3">
                {projects.map((project) => (
                  <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div>
                      <p className="font-medium">{project.name}</p>
                      <StatusBadge status={project.status} />
                    </div>
                    <div className="ml-4">
                      <Button variant="outline" size="sm" asChild>
                        <a href={`/projects/${project.id}`}>
                          View
                        </a>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No projects yet.</p>
                <Button variant="outline" className="mt-2" asChild>
                  <a href={`/projects?client=${client?.id}`}>
                    Create Project
                  </a>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Invoices Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Invoices
          </CardTitle>
          <CardDescription>
            All invoices associated with this client
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingInvoices ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading invoices...</p>
            </div>
          ) : invoices.length > 0 ? (
            <div className="space-y-3">
              {invoices.map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <div>
                        <p className="font-medium">Invoice {invoice.invoice_number}</p>
                        <p className="text-sm text-muted-foreground">
                          {invoice.project ? `Project: ${invoice.project.name}` : 'No project'}
                        </p>
                      </div>
                      <StatusBadge status={invoice.status} />
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {formatInvoiceCurrency(invoice.total_amount, invoice.currency as Currency)}
                    </div>
                    {invoice.due_date && (
                      <div className="text-sm text-muted-foreground flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Due: {new Date(invoice.due_date).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  <div className="ml-4">
                    <Button variant="outline" size="sm" asChild>
                      <a href={`/invoices/${invoice.id}`}>
                        View
                      </a>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No invoices yet.</p>
              <Button variant="outline" className="mt-2" asChild>
                <a href={`/invoices?client=${client?.id}`}>
                  Create Invoice
                </a>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Form Dialog */}
      <ClientForm
        open={showEditForm}
        onOpenChange={setShowEditForm}
        client={client}
        onSuccess={handleEditSuccess}
      />
    </div>
  )
}
